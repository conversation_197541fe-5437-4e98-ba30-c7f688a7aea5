<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/calendarTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:textAppearance="@style/TextAppearance.Material3.TitleLarge"
            android:textColor="@android:color/white"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Teaching Calendar" />

        <TextView
            android:id="@+id/calendarDate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            android:textColor="@android:color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/calendarTitle"
            tools:text="Today: April 2023" />
        
        <!-- Calendar Actions Container -->
        <LinearLayout
            android:id="@+id/calendarActions"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:gravity="end"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/calendarDate">
            
            <!-- Range Selection Toggle Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnToggleRange"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Select Range"
                android:textSize="12sp"
                android:backgroundTint="@color/accent"
                android:textColor="@android:color/white"
                app:cornerRadius="4dp"
                android:layout_marginEnd="8dp" />
        </LinearLayout>
        
        <!-- Month navigation bar -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/monthNavigationBar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@color/primary_variant"
            android:padding="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/calendarActions">

            <ImageButton
                android:id="@+id/btnPrevMonth"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Previous Month"
                android:src="@android:drawable/ic_media_previous"
                android:tint="@color/secondary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvMonthYear"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/secondary"
                android:textSize="18sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/btnNextMonth"
                app:layout_constraintStart_toEndOf="@+id/btnPrevMonth"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="April 2023" />

            <ImageButton
                android:id="@+id/btnNextMonth"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Next Month"
                android:src="@android:drawable/ic_media_next"
                android:tint="@color/secondary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Weekday header -->
        <LinearLayout
            android:id="@+id/layoutWeekdayHeader"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            android:background="@color/primary_variant"
            android:orientation="horizontal"
            android:padding="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/monthNavigationBar">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="SUN"
                android:textColor="@color/accent"
                android:textSize="12sp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="MON"
                android:textColor="@color/secondary"
                android:textSize="12sp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="TUE"
                android:textColor="@color/secondary"
                android:textSize="12sp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="WED"
                android:textColor="@color/secondary"
                android:textSize="12sp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="THU"
                android:textColor="@color/secondary"
                android:textSize="12sp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="FRI"
                android:textColor="@color/secondary"
                android:textSize="12sp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="SAT"
                android:textColor="@color/accent"
                android:textSize="12sp" />
        </LinearLayout>

        <!-- Calendar grid -->
        <GridView
            android:id="@+id/gridCalendar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            android:background="@color/primary_variant"
            android:horizontalSpacing="1dp"
            android:numColumns="7"
            android:padding="4dp"
            android:stretchMode="columnWidth"
            android:verticalSpacing="1dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layoutWeekdayHeader" />

        <!-- Selected date events section -->
        <TextView
            android:id="@+id/tvSelectedDate"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="@color/accent"
            android:gravity="center"
            android:padding="8dp"
            android:textColor="@android:color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/gridCalendar"
            tools:text="Events for April 09, 2023" />

        <!-- Empty state message -->
        <TextView
            android:id="@+id/noEventsText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="32dp"
            android:layout_marginEnd="16dp"
            android:gravity="center"
            android:text="No events scheduled"
            android:textColor="@android:color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvSelectedDate" />

        <!-- Events recycler view - Made this scrollable with more space -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerEvents"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="24dp"
            android:minHeight="200dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvSelectedDate"
            app:layout_constraintVertical_bias="0.0"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView> 