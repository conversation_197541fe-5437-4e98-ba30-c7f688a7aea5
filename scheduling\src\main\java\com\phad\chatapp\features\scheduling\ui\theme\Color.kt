package com.phad.chatapp.features.scheduling.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// Project specific colors
val DarkGreen = Color(0xFF006400)
val Dark_Green = Color(0xFF006400) // Duplicate for backward compatibility

// TTW App colors
val YellowAccent = Color(0xFFFFCC00)
val DarkPrimary = Color(0xFF000000)
val DarkBackground = Color(0xFF121212)
val DarkSurface = Color(0xFF1E1E1E)

// Enhanced UI color palette
val BlueAccent = Color(0xFF1E88E5)
val TealAccent = Color(0xFF00BCD4)
val SuccessGreen = Color(0xFF00C853)
val ErrorRed = Color(0xFFFF5252)
val NeutralGray = Color(0xFF9E9E9E)
val DarkGray = Color(0xFF333333)
val SurfaceElevated = Color(0xFF2C2C2C)
val NeutralCardSurface = Color(0xFF1F1F1F) // More neutral dark color for cards
val SlotActiveColor = Color(0xFFFFC107)
val SlotInactiveColor = Color(0xFF424242)