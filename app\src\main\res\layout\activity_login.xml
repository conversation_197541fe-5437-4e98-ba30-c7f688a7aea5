<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/login_page_bg"
    tools:context=".LoginActivity">
    
    <ImageView
        android:id="@+id/imageViewLogo"
        android:layout_width="80dp"
        android:layout_height="55dp"
        android:layout_marginTop="24dp"
        android:layout_marginStart="24dp"
        android:src="@drawable/app_logo_top_left"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textViewTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="26dp"
        android:layout_marginBottom="16dp"
        android:text="Teaching And Technical\nWing"
        android:textAlignment="textStart"
        android:textColor="#FFFFFF"
        android:textSize="28sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/textViewTagline" />

    <TextView
        android:id="@+id/textViewTagline"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="26dp"
        android:layout_marginBottom="35dp"
        android:text="Empowering Minds, Enriching Lives\nBridging Knowledge with Service"
        android:textAlignment="textStart"
        android:textColor="#CCFFFFFF"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/btnLoginAsUser" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnLoginAsUser"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:layout_marginHorizontal="26dp"
        android:layout_marginBottom="16dp"
        android:paddingVertical="16dp"
        android:background="@drawable/button_white_ripple"
        android:text="Log In As User"
        android:textAllCaps="false"
        android:textSize="19sp"
        android:textColor="@color/button_user_text_color"
        android:fontFamily="@font/kumbh_sans"
        android:textStyle="bold"
        android:stateListAnimator="@null"
        app:layout_constraintBottom_toTopOf="@+id/btnLoginAsAdmin"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnLoginAsAdmin"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:layout_marginHorizontal="26dp"
        android:layout_marginBottom="60dp"
        android:paddingVertical="16dp"
        android:background="@drawable/button_outline_ripple"
        android:text="Log In As Admin"
        android:textAllCaps="false"
        android:textSize="19sp"
        android:textColor="@color/button_admin_text_color"
        android:fontFamily="@font/kumbh_sans"
        android:textStyle="bold"
        android:stateListAnimator="@null"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- We'll hide these components initially but keep them so we don't break existing code -->
    <LinearLayout
        android:id="@+id/legacyLoginContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent">

        <EditText
            android:id="@+id/edit_text_roll_number"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone" />

        <EditText
            android:id="@+id/edit_text_email"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone" />

        <EditText
            android:id="@+id/edit_text_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone" />

        <EditText
            android:id="@+id/edit_text_passkey"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone" />

        <Button
            android:id="@+id/button_login"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone" />

        <Button
            android:id="@+id/btnLoginFirstTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone" />

        <TextView
            android:id="@+id/text_view_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 