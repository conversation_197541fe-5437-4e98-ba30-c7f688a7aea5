<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <EditText
        android:id="@+id/etEventTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:hint="Event Title"
        android:inputType="text"
        android:maxLines="1" />

    <EditText
        android:id="@+id/etEventDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:hint="Description"
        android:inputType="textMultiLine"
        android:minLines="2"
        android:maxLines="4" />

    <EditText
        android:id="@+id/etEventTimeSlot"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Time Slot (e.g., 9:00 AM - 11:00 AM)"
        android:inputType="text"
        android:maxLines="1" />

</LinearLayout> 