<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF"
    android:fitsSystemWindows="true">

    <!-- Scrollable content container -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="#FFFFFF"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            
            <!-- Custom toolbar with back button and title -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/toolbar_container"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="#0D0302"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:elevation="4dp">

                <ImageView
                    android:id="@+id/back_button"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_back_arrow"
                    android:contentDescription="@string/back_navigation"
                    android:clickable="true"
                    android:focusable="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <TextView
                    android:id="@+id/toolbar_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Remove Group"
                    android:textColor="#FFFFFF"
                    android:textSize="20sp"
                    android:layout_marginStart="12dp"
                    app:layout_constraintStart_toEndOf="@id/back_button"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Groups Header -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Available Groups"
                android:textSize="18sp"
                android:textColor="#006BFF"
                android:textStyle="bold"
                android:padding="16dp"/>

            <!-- Groups Recycler View -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/groups_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingBottom="16dp"
                tools:listitem="@layout/item_group_to_remove"/>

            <!-- Empty State View -->
            <LinearLayout
                android:id="@+id/empty_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="16dp"
                android:visibility="gone">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:src="@android:drawable/ic_dialog_info"
                    android:tint="#CCCCCC"/>

                <TextView
                    android:id="@+id/empty_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="No groups available"
                    android:textColor="#808080"
                    android:textSize="16sp"/>

            </LinearLayout>

            <!-- Loading Progress -->
            <ProgressBar
                android:id="@+id/loading_progress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone"/>

        </LinearLayout>
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout> 