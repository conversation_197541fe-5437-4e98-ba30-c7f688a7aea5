<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <TextView
            android:id="@+id/eventNameTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/statusChip"
            tools:text="Math Test 2025" />

        <TextView
            android:id="@+id/timestampTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="#757575"
            android:layout_marginTop="4dp"
            app:layout_constraintTop_toBottomOf="@id/eventNameTextView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/statusChip"
            tools:text="Created: 2025-05-24 10:15:30" />

        <com.google.android.material.chip.Chip
            android:id="@+id/statusChip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Live"
            android:textColor="@android:color/white"
            app:chipBackgroundColor="#4CAF50"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/totalMarkedTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="#757575"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/timestampTextView"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="Total Marked: 25" />

        <Button
            android:id="@+id/viewPendingButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="View Pending"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/totalMarkedTextView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <Button
            android:id="@+id/closeEventButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Close Event"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/viewPendingButton"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView> 