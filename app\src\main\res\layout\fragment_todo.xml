<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Header with title and back button -->
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@android:color/white"
            android:elevation="4dp"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageButton
                    android:id="@+id/backButton"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_close"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:contentDescription="Back" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tasks"
                    android:textColor="@android:color/black"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/backButton"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.appcompat.widget.Toolbar>

        <!-- Empty state view -->
        <LinearLayout
            android:id="@+id/emptyStateContainer"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/toolbar">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:src="@drawable/ic_todo"
                android:tint="#CCCCCC"
                android:contentDescription="No tasks" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="No tasks yet"
                android:textColor="#757575"
                android:textSize="18sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:paddingStart="32dp"
                android:paddingEnd="32dp"
                android:text="Add your first task by tapping the + button below"
                android:textColor="#9E9E9E"
                android:textSize="14sp" />
        </LinearLayout>

        <!-- RecyclerView for tasks -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/tasksRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:clipToPadding="false"
            android:paddingBottom="80dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            tools:listitem="@layout/item_task" />

        <!-- Loading indicator -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/toolbar" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- FAB for adding new tasks -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/addTaskFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_add"
        android:contentDescription="Add Task"
        app:tint="@android:color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout> 