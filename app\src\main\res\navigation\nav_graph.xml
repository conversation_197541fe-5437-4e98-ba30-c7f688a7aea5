<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/homeFragment">

    <fragment
        android:id="@+id/homeFragment"
        android:name="com.phad.chatapp.fragments.HomeFragment"
        android:label="Home"
        tools:layout="@layout/fragment_home" />

    <fragment
        android:id="@+id/chatFragment"
        android:name="com.phad.chatapp.ChatFragment"
        android:label="Chat"
        tools:layout="@layout/fragment_chat" />

    <fragment
        android:id="@+id/calendarFragment"
        android:name="com.phad.chatapp.features.calendar.ui.CalendarFragment"
        android:label="Calendar"
        tools:layout="@layout/fragment_calendar" />

    <fragment
        android:id="@+id/schedulingFragment"
        android:name="com.phad.chatapp.features.scheduling.SchedulingFragment"
        android:label="Scheduling"
        tools:layout="@layout/fragment_scheduling" />

    <fragment
        android:id="@+id/profileFragment"
        android:name="com.phad.chatapp.fragments.ProfileFragment"
        android:label="Profile"
        tools:layout="@layout/fragment_profile">
        
        <action
            android:id="@+id/action_profileFragment_to_libraryItemListFragment"
            app:destination="@id/libraryItemListFragment" />
    </fragment>

    <fragment
        android:id="@+id/libraryItemListFragment"
        android:name="com.phad.chatapp.fragments.LibraryItemListFragment"
        android:label="Library"
        tools:layout="@layout/fragment_library_item_list">
        <argument
            android:name="collectionPath"
            android:defaultValue="librarySections"
            app:argType="string" />
        <argument
            android:name="isSubcategory"
            android:defaultValue="false"
            app:argType="boolean" />
        <action
            android:id="@+id/action_libraryItemListFragment_self"
            app:destination="@id/libraryItemListFragment" />
    </fragment>

    <fragment
        android:id="@+id/attendanceManagerFragment"
        android:name="com.phad.chatapp.fragments.AttendanceManagerFragment"
        android:label="Attendance Manager"
        tools:layout="@layout/fragment_attendance_manager">
        <action
            android:id="@+id/action_attendanceManagerFragment_to_pendingAttendanceListFragment"
            app:destination="@id/pendingAttendanceListFragment" />
    </fragment>

    <fragment
        android:id="@+id/adminAttendanceApprovalFragment"
        android:name="com.phad.chatapp.fragments.AdminAttendanceApprovalFragment"
        android:label="Attendance Approval"
        tools:layout="@layout/fragment_admin_attendance_approval" />

    <fragment
        android:id="@+id/interviewFragment"
        android:name="com.phad.chatapp.features.interview.InterviewFragment"
        android:label="Interview"
        tools:layout="@layout/fragment_interview" />

    <fragment
        android:id="@+id/pendingAttendanceListFragment"
        android:name="com.phad.chatapp.fragments.PendingAttendanceListFragment"
        android:label="Pending Attendance"
        tools:layout="@layout/fragment_pending_attendance_list">
        <action
            android:id="@+id/action_pendingAttendanceListFragment_to_pendingAttendanceDetailFragment"
            app:destination="@id/pendingAttendanceDetailFragment" />
    </fragment>

    <fragment
        android:id="@+id/pendingAttendanceDetailFragment"
        android:name="com.phad.chatapp.fragments.PendingAttendanceDetailFragment"
        android:label="Pending Attendance Detail"
        tools:layout="@layout/fragment_pending_attendance_detail" >
        <action
            android:id="@+id/action_pendingAttendanceDetailFragment_to_fullScreenImageFragment"
            app:destination="@id/fullScreenImageFragment" />
    </fragment>

    <fragment
        android:id="@+id/fullScreenImageFragment"
        android:name="com.phad.chatapp.fragments.FullScreenImageFragment"
        android:label="Full Screen Image"
        tools:layout="@layout/fragment_full_screen_image">
        <argument
            android:name="imageUrl"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/addGroupFragment"
        android:name="com.phad.chatapp.fragments.AddGroupFragment"
        android:label="Add Group"
        tools:layout="@layout/fragment_add_group" />

    <fragment
        android:id="@+id/removeGroupFragment"
        android:name="com.phad.chatapp.fragments.RemoveGroupFragment"
        android:label="Remove Group"
        tools:layout="@layout/fragment_remove_group" />

</navigation> 