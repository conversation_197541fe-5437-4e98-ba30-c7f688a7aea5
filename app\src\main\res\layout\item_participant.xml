<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="3dp"
    app:cardBackgroundColor="#FFFFFF">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp"
        android:background="?attr/selectableItemBackground">

        <ImageView
            android:id="@+id/profile_image"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/bg_circle_avatar"
            android:backgroundTint="#006BFF"
            android:padding="8dp"
            android:src="@android:drawable/ic_menu_myplaces"
            app:tint="#FFFFFF"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/user_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#0D0302"
            android:textSize="16sp"
            app:layout_constraintBottom_toTopOf="@id/user_roll_number"
            app:layout_constraintEnd_toStartOf="@id/checkbox"
            app:layout_constraintStart_toEndOf="@id/profile_image"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="John Doe" />

        <TextView
            android:id="@+id/user_roll_number"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#808080"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/checkbox"
            app:layout_constraintStart_toEndOf="@id/profile_image"
            app:layout_constraintTop_toBottomOf="@id/user_name"
            tools:text="CSE123" />

        <CheckBox
            android:id="@+id/checkbox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:buttonTint="#006BFF"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView> 