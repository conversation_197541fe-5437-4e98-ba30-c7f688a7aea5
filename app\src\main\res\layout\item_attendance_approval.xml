<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="16dp"
    android:layout_marginEnd="16dp"
    android:layout_marginTop="8dp"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- User info section -->
        <TextView
            android:id="@+id/nameTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="Rabi Kumar Shaw" />

        <TextView
            android:id="@+id/rollNumberTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="#757575"
            android:layout_marginTop="4dp"
            app:layout_constraintTop_toBottomOf="@id/nameTextView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="2201CE49" />

        <TextView
            android:id="@+id/timestampTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textColor="#757575"
            android:layout_marginTop="4dp"
            app:layout_constraintTop_toBottomOf="@id/rollNumberTextView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="19 May 2025, 21:31" />

        <!-- Submitted image -->
        <ImageView
            android:id="@+id/submittedImageView"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginTop="16dp"
            android:scaleType="centerCrop"
            android:background="#EEEEEE"
            app:layout_constraintTop_toBottomOf="@id/timestampTextView"
            app:layout_constraintStart_toStartOf="parent"
            android:contentDescription="Submitted Image" />

        <!-- Action buttons -->
        <Button
            android:id="@+id/viewDetailsButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="View Details"
            android:textSize="12sp"
            android:layout_marginTop="16dp"
            app:layout_constraintTop_toBottomOf="@id/submittedImageView"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/approveButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Approve"
            android:textSize="12sp"
            android:layout_marginStart="8dp"
            app:layout_constraintTop_toTopOf="@id/viewDetailsButton"
            app:layout_constraintStart_toEndOf="@id/viewDetailsButton" />

        <Button
            android:id="@+id/rejectButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Reject"
            android:textSize="12sp"
            android:layout_marginStart="8dp"
            app:layout_constraintTop_toTopOf="@id/approveButton"
            app:layout_constraintStart_toEndOf="@id/approveButton" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView> 