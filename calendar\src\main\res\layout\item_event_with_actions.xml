<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <TextView
            android:id="@+id/tvEventTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textStyle="bold"
            android:text="Event Title" />

        <TextView
            android:id="@+id/tvEventTime"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="Time: 9:00 AM - 11:00 AM" />

        <TextView
            android:id="@+id/tvEventDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:maxLines="2"
            android:ellipsize="end"
            android:text="Event description will be displayed here..." />

        <TextView
            android:id="@+id/tvEventStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textStyle="italic"
            android:text="Status: SCHEDULED" />

        <LinearLayout
            android:id="@+id/actionButtonsLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end"
            android:layout_marginTop="8dp">

            <Button
                android:id="@+id/btnDelete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Delete"
                android:textSize="12sp"
                android:backgroundTint="#F44336"
                style="@style/Widget.MaterialComponents.Button" />

            <Button
                android:id="@+id/btnViewDetails"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="Details"
                android:textSize="12sp"
                android:backgroundTint="#2196F3"
                style="@style/Widget.MaterialComponents.Button" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView> 