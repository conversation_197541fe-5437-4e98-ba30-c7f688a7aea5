<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".UserMigrationActivity">

    <TextView
        android:id="@+id/text_view_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="User Migration Tool"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textAlignment="center"
        android:layout_marginTop="32dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/text_view_explanation"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="This tool will migrate users from the TWApp database (Student, Admin1, and Admin2 collections) to the main app's users collection."
        android:textSize="16sp"
        android:layout_marginTop="24dp"
        app:layout_constraintTop_toBottomOf="@id/text_view_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/text_view_details"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="• Students will have userType='Student' and year=1\n• Subcoordinators will have userType='Admin1' and year=2\n• Coordinators will have userType='Admin2' and year=3\n• All users will have description='together we can'"
        android:textSize="14sp"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/text_view_explanation"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <Button
        android:id="@+id/button_start_migration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Start Migration"
        android:layout_marginTop="32dp"
        app:layout_constraintTop_toBottomOf="@id/text_view_details"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/button_start_migration"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/text_view_status"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text=""
        android:textSize="16sp"
        android:textAlignment="center"
        android:layout_marginTop="16dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/progress_bar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 