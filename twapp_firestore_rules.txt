// Firestore rules for TWApp database
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Basic function to check if a user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Function to check if user is an admin
    function isAdmin() {
      return isAuthenticated() && 
        (request.auth.token.admin == true || 
         exists(/databases/$(database)/documents/admins/$(request.auth.uid)));
    }
    
    // Student collection - read access for authenticated users, write access for admins only
    match /Student/{document=**} {
      // Read access for any authenticated user
      allow read: if isAuthenticated();
      
      // Write access only for admins
      allow write: if isAdmin();
    }
    
    // Admin1 collection - same rules as Student collection
    match /Admin1/{document=**} {
      // Read access for any authenticated user
      allow read: if isAuthenticated();
      
      // Write access only for admins
      allow write: if isAdmin();
    }
    
    // Admin2 collection - same rules as Student collection
    match /Admin2/{document=**} {
      // Read access for any authenticated user
      allow read: if isAuthenticated();
      
      // Write access only for admins
      allow write: if isAdmin();
    }
    
    // Test collection for development - full access for authenticated users
    match /test/{document=**} {
      allow read, write: if isAuthenticated();
    }
    
    // Users collection - users can read/write their own data, admins can read/write all
    match /users/{userId} {
      allow read: if isAuthenticated() && (request.auth.uid == userId || isAdmin());
      allow write: if isAuthenticated() && (request.auth.uid == userId || isAdmin());
    }
    
    // Default deny all other collections
    match /{document=**} {
      allow read, write: if isAdmin();
    }
  }
} 