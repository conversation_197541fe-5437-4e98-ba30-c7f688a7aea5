<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/library_nav_graph"
    app:startDestination="@id/librarySectionsFragment">

    <fragment
        android:id="@+id/librarySectionsFragment"
        android:name="com.phad.chatapp.fragments.LibrarySectionsFragment"
        android:label="Library Sections"
        tools:layout="@layout/fragment_library_sections">
        <action
            android:id="@+id/action_librarySectionsFragment_to_librarySubCategoriesFragment"
            app:destination="@id/librarySubCategoriesFragment" />
    </fragment>

    <fragment
        android:id="@+id/librarySubCategoriesFragment"
        android:name="com.phad.chatapp.fragments.LibrarySubCategoriesFragment"
        android:label="Library Subcategories"
        tools:layout="@layout/fragment_library_sub_categories">
        <argument
            android:name="sectionId"
            app:argType="string" />
        <action
            android:id="@+id/action_librarySubCategoriesFragment_to_libraryItemListFragment"
            app:destination="@id/libraryItemListFragment" />
    </fragment>

    <fragment
        android:id="@+id/libraryItemListFragment"
        android:name="com.phad.chatapp.fragments.LibraryItemListFragment"
        android:label="Library Items"
        tools:layout="@layout/fragment_library_item_list">
        <argument
            android:name="subCategoryId"
            app:argType="string" />
        <action
            android:id="@+id/action_libraryItemListFragment_to_libraryFilesFragment"
            app:destination="@id/libraryFilesFragment" />
        <action
            android:id="@+id/action_libraryItemListFragment_to_libraryItemListFragment"
            app:destination="@id/libraryItemListFragment" />
    </fragment>

    <fragment
        android:id="@+id/libraryFilesFragment"
        android:name="com.phad.chatapp.fragments.LibraryFilesFragment"
        android:label="Library Files"
        tools:layout="@layout/fragment_library_files">
        <argument
            android:name="filesCollectionPath"
            app:argType="string" />
    </fragment>

</navigation> 