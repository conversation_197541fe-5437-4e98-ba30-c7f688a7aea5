// Firebase Storage Rules
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Allow read/write access to all users when authenticated
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}

// Firebase Firestore Rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read/write access to all authenticated users
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
} 