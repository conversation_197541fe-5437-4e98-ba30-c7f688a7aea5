<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#0D0302"
    android:fitsSystemWindows="true"
    tools:context=".ChatActivity">

    <!-- Top Bar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="#0D0302"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- Back Button -->
            <ImageView
                android:id="@+id/back_button"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_back_arrow"
                android:contentDescription="Back"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:tint="#FFFFFF" />

            <!-- User Name -->
            <TextView
                android:id="@+id/textTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:textColor="#FFFFFF"
                android:textSize="18sp"
                android:maxLines="1"
                android:ellipsize="end"
                app:layout_constraintStart_toEndOf="@id/back_button"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>

    <!-- Content container with white background -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="#FFFFFF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <!-- Messages RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerMessages"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:clipToPadding="false"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/message_input_container"
            tools:listitem="@layout/item_message_received" />

        <!-- Message Input Container -->
        <androidx.cardview.widget.CardView
            android:id="@+id/message_input_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            app:cardCornerRadius="24dp"
            app:cardElevation="2dp"
            app:layout_constraintBottom_toBottomOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="8dp">

                <!-- Attachment Button -->
                <ImageButton
                    android:id="@+id/attach_button"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_attach_file"
                    android:tint="#006BFF"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <!-- Message Input Field -->
                <EditText
                    android:id="@+id/messageInput"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:hint="Type a message..."
                    android:inputType="textMultiLine"
                    android:maxLines="4"
                    android:minHeight="40dp"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:textSize="16sp"
                    app:layout_constraintStart_toEndOf="@id/attach_button"
                    app:layout_constraintEnd_toStartOf="@id/buttonSend"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <!-- Send Button -->
                <ImageButton
                    android:id="@+id/buttonSend"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_send"
                    android:tint="#006BFF"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.cardview.widget.CardView>

        <!-- Loading Progress -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 