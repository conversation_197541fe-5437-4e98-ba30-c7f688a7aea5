<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    tools:context=".fragments.NssQRScanFragment">

    <!-- Camera preview will be added programmatically -->
    <androidx.camera.view.PreviewView
        android:id="@+id/camera_preview"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- Overlay UI -->
    <androidx.compose.ui.platform.ComposeView
        android:id="@+id/compose_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</FrameLayout>
