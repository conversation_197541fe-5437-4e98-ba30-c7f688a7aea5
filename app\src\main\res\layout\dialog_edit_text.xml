<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="16dp"
    app:cardBackgroundColor="#0D0302">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/dialog_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Edit Group Name"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <EditText
            android:id="@+id/edit_text_field"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_message_input"
            android:padding="12dp"
            android:textColor="#FFFFFF"
            android:textColorHint="#AAAAAA"
            android:layout_marginBottom="24dp"
            android:minHeight="48dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/cancel_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="CANCEL"
                android:textSize="14sp"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:layout_marginEnd="8dp"
                android:backgroundTint="#006BFF"
                android:textColor="#FFFFFF" />

            <Button
                android:id="@+id/save_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="SAVE"
                android:textSize="14sp"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:layout_marginStart="8dp"
                android:backgroundTint="#006BFF"
                android:textColor="#FFFFFF" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView> 