<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="16dp"
    app:cardBackgroundColor="#0D0302">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/group_info_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Group Info"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Description"
            android:textColor="#FFCC00"
            android:textSize="14sp"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/group_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Group description goes here"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Created by"
            android:textColor="#FFCC00"
            android:textSize="14sp"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/group_creator"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Creator name"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Created on"
            android:textColor="#FFCC00"
            android:textSize="14sp"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/group_created_date"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="January 1, 2023"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Participants"
            android:textColor="#FFCC00"
            android:textSize="14sp"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/group_participants_count"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="5 participants"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />

        <Button
            android:id="@+id/close_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="CLOSE"
            android:backgroundTint="#006BFF"
            android:textColor="#FFFFFF"
            android:layout_marginTop="24dp" />
    </LinearLayout>
</androidx.cardview.widget.CardView> 