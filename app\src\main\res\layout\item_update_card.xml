<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="300dp"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Category badge and important indicator -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/categoryBadge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/category_badge_background"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:textColor="#FFFFFF"
                android:textSize="10sp"
                android:textStyle="bold"
                tools:text="ACADEMIC"/>

            <ImageView
                android:id="@+id/importantIndicator"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginStart="8dp"
                android:src="@android:drawable/ic_dialog_info"
                android:tint="#FF5722"
                android:visibility="gone"
                tools:visibility="visible"/>
        </LinearLayout>

        <!-- Author info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/authorImageView"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/default_profile_image"
                app:civ_border_width="1dp"
                app:civ_border_color="#EEEEEE"/>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginStart="12dp">

                <TextView
                    android:id="@+id/authorNameTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:textColor="?attr/colorOnSurface"
                    tools:text="John Doe"/>

                <TextView
                    android:id="@+id/timestampTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:textColor="?attr/colorOnSurface"
                    android:alpha="0.6"
                    tools:text="2 hours ago"/>
            </LinearLayout>
        </LinearLayout>

        <!-- Title (if any) -->
        <TextView
            android:id="@+id/titleTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="?attr/colorOnSurface"
            tools:text="Update Title"/>

        <!-- Content -->
        <TextView
            android:id="@+id/contentTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textSize="14sp"
            android:maxLines="4"
            android:ellipsize="end"
            android:textColor="?attr/colorOnSurface"
            tools:text="This is a sample update content with some text that might span multiple lines to show how it looks in the actual UI."/>

        <!-- Event Date (if any) -->
        <LinearLayout
            android:id="@+id/eventDateContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@android:drawable/ic_menu_my_calendar"
                app:tint="#757575"/>

            <TextView
                android:id="@+id/eventDateText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:textSize="12sp"
                android:textColor="?attr/colorOnSurface"
                tools:text="Thu, May 5, 2023"/>
        </LinearLayout>

        <!-- Location (if any) -->
        <LinearLayout
            android:id="@+id/locationContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@android:drawable/ic_menu_compass"
                app:tint="#757575"/>

            <TextView
                android:id="@+id/locationText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:textSize="12sp"
                android:textColor="?attr/colorOnSurface"
                tools:text="Main Auditorium, Building 4"/>
                
            <ImageButton
                android:id="@+id/locationMapButton"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_dialog_map"
                android:background="?attr/selectableItemBackgroundBorderless"
                app:tint="#1976D2"/>
        </LinearLayout>

        <!-- Media Image (if any) -->
        <ImageView
            android:id="@+id/imageView"
            android:layout_width="match_parent"
            android:layout_height="160dp"
            android:layout_marginTop="12dp"
            android:scaleType="centerCrop"
            android:adjustViewBounds="true"
            android:visibility="gone"
            tools:visibility="visible"
            tools:src="@android:drawable/ic_menu_gallery"/>

        <!-- External link (if any) -->
        <androidx.cardview.widget.CardView
            android:id="@+id/externalLinkCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:cardCornerRadius="8dp"
            app:cardBackgroundColor="#F5F5F5"
            android:visibility="gone"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="12dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_view"
                    app:tint="#757575"/>

                <TextView
                    android:id="@+id/externalLinkText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="12dp"
                    android:textSize="14sp"
                    android:ellipsize="middle"
                    android:singleLine="true"
                    tools:text="https://example.com"/>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Document (if any) -->
        <androidx.cardview.widget.CardView
            android:id="@+id/documentCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:cardCornerRadius="8dp"
            app:cardBackgroundColor="#F5F5F5"
            android:visibility="gone"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="12dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_agenda"
                    app:tint="#757575"/>

                <TextView
                    android:id="@+id/documentNameText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="12dp"
                    android:textSize="14sp"
                    android:ellipsize="middle"
                    android:singleLine="true"
                    tools:text="document_name.pdf"/>
            </LinearLayout>
        </androidx.cardview.widget.CardView>
        
        <!-- Tags container -->
        <com.google.android.material.chip.ChipGroup
            android:id="@+id/tagsChipGroup"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            app:chipSpacingHorizontal="4dp"
            android:visibility="gone"
            tools:visibility="visible"/>
    </LinearLayout>
</androidx.cardview.widget.CardView> 