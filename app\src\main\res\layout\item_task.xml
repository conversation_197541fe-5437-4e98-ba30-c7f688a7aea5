<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="16dp"
    android:layout_marginEnd="16dp"
    android:layout_marginTop="8dp"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <CheckBox
            android:id="@+id/taskCheckbox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="@id/taskTitle" />

        <TextView
            android:id="@+id/taskTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:textColor="@android:color/black"
            android:textSize="16sp"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintStart_toEndOf="@id/taskCheckbox"
            app:layout_constraintEnd_toStartOf="@id/deleteButton"
            app:layout_constraintTop_toTopOf="parent"
            android:text="Task title goes here" />

        <TextView
            android:id="@+id/taskDueDate"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="4dp"
            android:textColor="#757575"
            android:textSize="12sp"
            app:layout_constraintStart_toEndOf="@id/taskCheckbox"
            app:layout_constraintEnd_toStartOf="@id/deleteButton"
            app:layout_constraintTop_toBottomOf="@id/taskTitle"
            android:text="Due: Today" />

        <ImageButton
            android:id="@+id/deleteButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_delete"
            android:contentDescription="Delete task"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView> 