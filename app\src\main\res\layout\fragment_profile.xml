<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#0D0302">

    <ImageView
        android:id="@+id/ivLogo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:src="@drawable/ic_book"
        android:tint="#FFEB3B"
        android:contentDescription="Logo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivTaskIcon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="16dp"
        android:src="@drawable/ic_tasks"
        android:tint="#FFFFFF"
        android:contentDescription="Tasks"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintEnd_toStartOf="@+id/ivChatbotIcon"
        app:layout_constraintTop_toTopOf="@+id/ivLogo"
        app:layout_constraintBottom_toBottomOf="@+id/ivLogo" />

    <ImageView
        android:id="@+id/ivChatbotIcon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="16dp"
        android:src="@drawable/ic_robot"
        android:tint="#FFFFFF"
        android:contentDescription="Chatbot"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ivLogo"
        app:layout_constraintBottom_toBottomOf="@+id/ivLogo" />

    <!-- Statistics Section -->
    <TextView
        android:id="@+id/tvEvents"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="4/5"
        android:textColor="@android:color/white"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/tvEventsLabel"
        app:layout_constraintEnd_toStartOf="@+id/tvClasses"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        android:layout_marginTop="240dp" />

    <TextView
        android:id="@+id/tvEventsLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Events"
        android:textColor="#757575"
        android:textSize="12sp"
        app:layout_constraintBottom_toTopOf="@+id/cardViewProfile"
        app:layout_constraintEnd_toEndOf="@+id/tvEvents"
        app:layout_constraintStart_toStartOf="@+id/tvEvents"
        app:layout_constraintTop_toBottomOf="@+id/tvEvents"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/tvClasses"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="7/9"
        android:textColor="@android:color/white"
        android:textSize="36sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/tvClassesLabel"
        app:layout_constraintEnd_toStartOf="@+id/tvMeetings"
        app:layout_constraintStart_toEndOf="@+id/tvEvents"
        app:layout_constraintTop_toTopOf="@+id/tvEvents" />

    <TextView
        android:id="@+id/tvClassesLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Classes"
        android:textColor="#757575"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@+id/tvEventsLabel"
        app:layout_constraintEnd_toEndOf="@+id/tvClasses"
        app:layout_constraintStart_toStartOf="@+id/tvClasses"
        app:layout_constraintTop_toTopOf="@+id/tvEventsLabel" />

    <TextView
        android:id="@+id/tvMeetings"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="3/3"
        android:textColor="@android:color/white"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/tvMeetingsLabel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvClasses"
        app:layout_constraintTop_toTopOf="@+id/tvEvents" />

    <TextView
        android:id="@+id/tvMeetingsLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Meetings"
        android:textColor="#757575"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@+id/tvClassesLabel"
        app:layout_constraintEnd_toEndOf="@+id/tvMeetings"
        app:layout_constraintStart_toStartOf="@+id/tvMeetings"
        app:layout_constraintTop_toTopOf="@+id/tvClassesLabel" />

    <!-- Profile Card -->
    <androidx.cardview.widget.CardView
        android:id="@+id/cardViewProfile"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="340dp"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:layout_marginBottom="0dp"
        app:cardBackgroundColor="@android:color/white"
        app:cardCornerRadius="30dp"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="24dp">

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fillViewport="true"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tvName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Shikhar Yadav"
                        android:textColor="@android:color/black"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tvLocation"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Uttar Pradesh"
                        android:textColor="#757575"
                        android:textSize="16sp"
                        app:layout_constraintStart_toStartOf="@+id/tvName"
                        app:layout_constraintTop_toBottomOf="@+id/tvName" />

                    <TextView
                        android:id="@+id/tvEmail"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="<EMAIL>"
                        android:textColor="@android:color/black"
                        android:textSize="14sp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="4dp" />

                    <TextView
                        android:id="@+id/tvPhone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="83942935893"
                        android:textColor="@android:color/black"
                        android:textSize="14sp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvEmail"
                        android:layout_marginTop="4dp" />

                    <View
                        android:id="@+id/divider"
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:background="#E0E0E0"
                        android:layout_marginTop="24dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvLocation" />

                    <TextView
                        android:id="@+id/tvRollNumber"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="2401EC99"
                        android:textColor="@android:color/black"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/divider"
                        android:layout_marginTop="16dp" />

                    <TextView
                        android:id="@+id/tvEmail2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="<EMAIL>"
                        android:textColor="#757575"
                        android:textSize="14sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvRollNumber" />

                    <TextView
                        android:id="@+id/tvAcademicGroup"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Academic Group: 25"
                        android:textColor="@android:color/black"
                        android:textSize="14sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvEmail2"
                        android:layout_marginTop="16dp" />

                    <TextView
                        android:id="@+id/tvNssGroup"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="NSS Group: -1"
                        android:textColor="@android:color/black"
                        android:textSize="14sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvAcademicGroup"
                        android:layout_marginTop="4dp" />

                    <TextView
                        android:id="@+id/tvTopicsHeader"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Topics of Interest"
                        android:textColor="@android:color/black"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvNssGroup"
                        android:layout_marginTop="24dp" />

                    <TextView
                        android:id="@+id/tvTopic1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="English"
                        android:textColor="#757575"
                        android:textSize="14sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvTopicsHeader"
                        android:layout_marginTop="8dp" />

                    <TextView
                        android:id="@+id/tvTopic2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="History"
                        android:textColor="#757575"
                        android:textSize="14sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvTopic1"
                        android:layout_marginTop="4dp" />

                    <TextView
                        android:id="@+id/tvTopic3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Technology"
                        android:textColor="#757575"
                        android:textSize="14sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvTopic2"
                        android:layout_marginTop="4dp" />

                    <Button
                        android:id="@+id/btnLogout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="LOGOUT"
                        android:textColor="#FFFFFF"
                        android:textStyle="bold"
                        android:background="#2196F3"
                        android:padding="12dp"
                        android:layout_marginTop="24dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_marginBottom="16dp"
                        app:layout_constraintTop_toBottomOf="@+id/tvTopic3"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </ScrollView>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout> 