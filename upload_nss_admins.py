import firebase_admin
from firebase_admin import credentials, firestore
import csv
import io

# Service account key as a dict
service_account_key = ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# Initialize Firebase Admin SDK using the dict
try:
    cred = credentials.Certificate(service_account_key)
    if not firebase_admin._apps:
        firebase_admin.initialize_app(cred)
    db = firestore.client()
    print("Successfully connected to Firebase.")
except Exception as e:
    print(f"Error connecting to Firebase: {e}")
    print("Please ensure your service account key is correct.")
    exit()

# Updated CSV data
csv_data = """Roll_Number,Name,Contact_Number,College_Email,Personal_Email,Year,image_url,Academic_Group,NSS_Group,Gender,Teaching_wing
2401ME27,VIRAJ MANDAVKAR,**********,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1j0mdtZNG8dxYf6-7MyYuwv1SeLJjEiEM/view?usp=drivesdk,21,15,Male,Yes
2401ME71,Harshit Sayal,**********,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1ogVc3Ow5FJZz2wLXxq7N0lEKxhyjANDK/view?usp=drivesdk,21,2,Male,Yes
2402MT05,Dikshit Verma ,**********,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1ML-1996kzlt9z9Yma7tsP15OhJdpE0HB/view?usp=drivesdk,4,32,Male,No
2401CS45,DARLA SRAVAN KUMAR,**********,<EMAIL>,<EMAIL>,2,https://drive.google.com/drive/u/0/folders/1NuU_z-RN6LOekdJ-FplA0stPygCcEpAC,14,26,Male,No
2401MC20,RANVEER GUPTA,**********,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1PKhFbT1ys8wgW3Au4V07YM6B0aaavQ59/view?usp=drivesdk,20,30,Male,Yes
2401CE10,Shivam kumar,**********,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1-JflCgmy4UQSkqBBCX67b5mk89Tcl_dc/view?usp=drivesdk,11,31,Male,Yes
2403EE05,B.JAWAHAR REDDY,8309122630,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1XdGvec51tA670OX_Dbt2O9KO9SHJEcKN/view?usp=drivesdk,5,24,Male,No
2401ME61,Aditya Raj,8002950538,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1Ngze0X9e-IxtwjOfUe3zy_ujKj6AS_vh/view?usp=drivesdk,22,25,Male,No
Pranay Dev,Pranay Dev,+91 8789478742,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/12E_9YFomDAy86z14NHUIpF2DasfZ5Q9e/view,8,10,Male,No
2403ME04,Patil Arya Nitin ,7709152843,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1BiA4X6RyiTqTC1IIp3mPcFTWUmqR1U4b/view?usp=drivesdk,5,31,Female,No
2401ME49,Riju Mondal,7908986725,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/195_r2aT30uyU4z6ju2D4WKLSIgj1jX_i/view?usp=drivesdk,23,11,Male,No
2402MT08,Piyush Kumar ,7905723471,<EMAIL>,<EMAIL>,2,https://drive.google.com/drive/folders/1zueFyCZ4onrpEX_VsIpqQhbewKRq7w4t,4,8,Male,No
2401ME59,Yash Mayur Modi,6352020828,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1EePeiiTKC1KbQ_JA-yAjMgGJ5tgQ58-6/view?usp=sharing,21,16,Male,Yes
2401ME60,Amit Kumar Meena ,9352835543,<EMAIL>,<EMAIL>,2,https://drive.google.com/drive/folders/1BIsXuPqeJjqUUoEnh0RNSnvUzNEw9AIK,23,32,Male,Yes
2401EC17,Sai Gayathri ,7569818259,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1R2wMqIQHHZwXsW7zurSNp9yBP80GALDy/view?usp=drivesdk,17,30,Female,No
2401CE27,Kumari Jagrati,6395326149,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/18dfdv1_4eykAhzhW05-kGCBern6Pfp5O/view?usp=sharing,10,24,Female,Yes
2401ME13,Harsh Verma,9045120527,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1Uo0Fm3JrShuxizcX1e4K5nRnlCC1Hz91/view?usp=sharing,21,28,Male,Yes
2403CT04,Priyanshu Purohit ,7340710615,<EMAIL> ,<EMAIL> ,2,https://drive.google.com/file/d/17GI0qL5k1Y8J2XrUaVi8K3aTHzwT3a0V/view?usp=drivesdk,4,24,Male,No
2402PC01,Varada Anirudh ,8247533648,<EMAIL>,<EMAIL>,2,https://drive.google.com/drive/folders/1rwfXuFnxbDB5KIX_yglXdHCZQIcQz3Oj,3,7,Male,No
2401ME42,VIJAY MANOJ PATHELLA ,9121671830,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1EVPJSl3FinKVHfIbhZRHA3QtEShlKONC/view?usp=drivesdk,21,5,Male,No
2402ST06,Anshika Garg,7696216163,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1RsmXCUCLjIcVfwLX_zqxHBFGtuz5C7dm/view?usp=sharing,2,17,Female,Yes
2401MC26,Anish Kumar ,7367072522,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1ttl9lBw7GP8AFx3W8I3KSJNxPdg8-FrM/view?usp=drivesdk,20,14,Male,Yes
2401CE23,HIMANSHI,8278922113,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1LDtt79i8-2WU6_WhOR-Pm7W3jHxM_XFc/view?usp=drivesdk,11,13,Female,No
2401CT02,Mehal Srivastava ,9535896013,<EMAIL>,<EMAIL>,2,https://drive.google.com/drive/folders/1zvRBOy0hGjzmS4yMm7Tqi8vX7tiSnA4D,9,9,Female,Yes
2401CT09,Samiksha katariya ,9828861697,<EMAIL> in,<EMAIL>,2,https://drive.google.com/file/d/17qk5R5DevCyiOWD29kVlFccNChvmu9No/view?usp=drivesdk,10,11,Female,No
2403MM03,Tharun Thirupathi ,7780390445,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/19cxLgP82Yp-oiFYXkCCLmlVj8eAXz6BV/view?usp=drive_link,6,5,Male,No
2401MM32,Parnava Maitra,9593396993,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1cmGz9dADmlrigl8LZeuk5goS-6at5WLc/view?usp=drivesdk,24,22,Male,Yes
2401EC11,Tanishk Raj ,7033143473,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1LdqSaMtImgMr8KWbQFnlg7tYW0RfZ8Ua/view?usp=drivesdk,17,7,Male,Yes
2401CT30,Kshitij Singh ,7088657157,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1kEWVPN2DYlsiFjOEHPEhMi5JE3mtr1_u/view?usp=drivesdk,9,18,Male,No
2401MM10,SHANKHADEEP DAS,8159826878,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1Y02CuplN-AUJ7dW-m6uu2vFQk_LaBygX/view?usp=drivesdk,24,6,Male,No
2403CT02,Charu Garg,8595793227,<EMAIL>,<EMAIL>,2,https://drive.google.com/drive/folders/1XzSbtqZiRiP3n5H2cY6jrNdxbLwN6IVe?usp=sharing,4,11,Female,No
2402PC02,Neha Sree Kuppam,9573692502,<EMAIL>,<EMAIL>,2,https://drive.google.com/drive/folders/194ETHnFLfmQ9ww09OkGBi-wJuOnqXGwW,3,29,Female,No
2401EC19,Ayantika Halder ,8584014805,<EMAIL>,<EMAIL>,2,https://photos.app.goo.gl/UYsVnE2aaY5hLf6i8,18,23,Female,No
2401CT04,AYUSH SEN,7000257907,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1V7zbrEpEJpInhPAYxLZFkP0hYkWgSTTb/view?usp=drivesdk,9,8,Male,No
2401CE14,Shreya Yadav ,8467935303,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1Ry_jMHIJsOnXWIGPTiBZDekKUvtpbHOf/view?usp=drivesdk,11,11,Female,No
2402GT05,Shaurya Singh ,9234763129,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/**************************-8mSjdt/view?usp=drivesdk,2,20,Female,No
2401me14,B.praneeth,9441592387,<EMAIL>,<EMAIL>,2,https://photos.app.goo.gl/tyG9omrar7MzJTvF8,23,28,Male,No
2401CB06 ,Abhishek kumar ,79090 88743 ,<EMAIL>,<EMAIL> ,2,,8,31,Male,No
2401MC34,Kingshuk Haldar,8100518784,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/18H3oPwI9gvE5pz-SjNABw2xWr11cBX2I/view?usp=sharing,20,9,Male,Yes
2401CB23,DEVYANSH PANDEY,9198333486,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1aC2VfC0uWH9BZNZHeaXgvF3qGAMa6HE4/view?usp=drivesdk,7,32,Male,Yes
2302MT05,PT BHOODEV BHUSHAN MISHRA ,9129359974,<EMAIL>,<EMAIL>,3,,1,1,Male,No
2401ME52,Ayushkar Nath,6033055994,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1BOb_SPow2CgmJTGnHzJ28KIg57y3BpbN/view?usp=drivesdk,22,20,Male,No
2401CE51,MITALI AWASTHI,9555535910,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/12g5v3dtPgDoeHsI7l0I46RR5320hp0zk/view?usp=sharing,10,25,Female,Yes
2301CS44,Sai Vardhan,6300232527,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/174BvpxGi-cDIEu-dt51IomuCo6zvujnE/view?usp=drivesdk,1,32,Male,No
2401EE10,Romir Zadoo,7303770116,<EMAIL>,<EMAIL>,2,https://drive.google.com/file/d/1rCN3q-4eSCMdlwuFegoey3Zg1RlSIy9O/view?usp=drive_link,15,18,Male,No
2301MC51,Aditya Gupta,9410408989,<EMAIL>,<EMAIL>,3,https://drive.google.com/file/d/1ZnPNJcHl-3aIxaKhsK47E6jSFYAND0Pp/view?usp=drive_link,0,0,Male,Yes
"""

# Use io.StringIO to treat the string data as a file
csv_file = io.StringIO(csv_data)

# Parse the CSV data
reader = csv.DictReader(csv_file)

collection_name = 'NSS_ADMINS'

# Loop through each row in the CSV
for row in reader:
    try:
        roll_number = row.get('Roll_Number', '').strip()
        if not roll_number:
            print(f"Skipping row due to empty Roll_Number: {row}")
            continue

        # Data to be added to Firestore with correct types
        data_to_upload = {}
        for key, value in row.items():
            stripped_key = key.strip()
            stripped_value = value.strip() if value else ""

            if stripped_key in ['Year', 'Academic_Group', 'NSS_Group']:
                try:
                    # Convert to number, default to 0 if empty/invalid
                    data_to_upload[stripped_key] = int(stripped_value) if stripped_value else 0
                except (ValueError, TypeError):
                    data_to_upload[stripped_key] = 0
            elif stripped_key == 'Teaching_wing':
                # Convert to boolean
                data_to_upload[stripped_key] = stripped_value.lower() == 'yes'
            else:
                data_to_upload[stripped_key] = stripped_value

        db.collection(collection_name).document(roll_number).set(data_to_upload)
        print(f"Successfully uploaded data for Roll Number: {roll_number}")

    except Exception as e:
        print(f"Error uploading data for row: {row}")
        print(f"Error: {e}")

print(f"\nData upload to '{collection_name}' collection complete.") 