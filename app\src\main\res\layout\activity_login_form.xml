<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:fitsSystemWindows="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/login_page_bg"
        tools:context=".LoginFormActivity">

        <ImageView
            android:id="@+id/imageViewLogo"
            android:layout_width="80dp"
            android:layout_height="55dp"
            android:layout_marginTop="24dp"
            android:layout_marginStart="24dp"
            android:src="@drawable/app_logo_top_left"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/btnBack"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="24dp"
            android:src="@drawable/ic_close"
            android:tint="#FFFFFF"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textViewTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="26dp"
            android:layout_marginTop="150dp"
            android:text="Teaching And Technical\nWing"
            android:textAlignment="textStart"
            android:textColor="#FFFFFF"
            android:textSize="28sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imageViewLogo" />

        <TextView
            android:id="@+id/textViewTagline"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="26dp"
            android:layout_marginTop="8dp"
            android:text="Empowering Minds, Enriching Lives\nBridging Knowledge with Service"
            android:textAlignment="textStart"
            android:textColor="#CCFFFFFF"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textViewTitle" />

        <ImageView
            android:id="@+id/bottomCurve"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="-20dp"
            android:scaleType="fitXY"
            android:src="@drawable/login_bottom_white_curve"
            app:layout_constraintTop_toBottomOf="@+id/textViewTagline"
            app:layout_constraintBottom_toBottomOf="parent" />

        <TextView
            android:id="@+id/textViewLogIn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="32dp"
            android:layout_marginTop="65dp"
            android:text="LOG IN"
            android:textColor="#222222"
            android:textSize="32sp"
            android:fontFamily="@font/kumbh_sans"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/bottomCurve" />

        <TextView
            android:id="@+id/textViewLoginType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="32dp"
            android:layout_marginTop="4dp"
            android:text="As User"
            android:fontFamily="@font/kumbh_sans"
            android:textColor="#1E1716"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textViewLogIn" />

        <!-- Roll Number input -->
        <LinearLayout
            android:id="@+id/layoutRollNumber"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="32dp"
            android:layout_marginTop="24dp"
            android:background="@drawable/edit_text_background"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingVertical="8dp"
            app:layout_constraintTop_toBottomOf="@id/textViewLoginType">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:alpha="0.6"
                android:src="@drawable/person_head_roll_number_icon" />

            <EditText
                android:id="@+id/etRollNumber"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:background="@null"
                android:hint="Roll Number"
                android:inputType="text"
                android:textColorHint="#7b7b7b"
                android:textColor="#7b7b7b"
                android:textSize="16sp" />

            <ImageView
                android:id="@+id/rollNumberCheckMark"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/tick_icon"
                android:visibility="gone" />
        </LinearLayout>

        <!-- Email input -->
        <LinearLayout
            android:id="@+id/layoutEmail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="32dp"
            android:layout_marginTop="24dp"
            android:background="@drawable/edit_text_background"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingVertical="8dp"
            app:layout_constraintTop_toBottomOf="@id/layoutRollNumber">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:alpha="0.6"
                android:src="@drawable/email_icon" />

            <EditText
                android:id="@+id/etEmail"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:background="@null"
                android:hint="<EMAIL>"
                android:inputType="textEmailAddress"
                android:textColorHint="#7b7b7b"
                android:textColor="#7b7b7b"
                android:textSize="16sp" />

            <ImageView
                android:id="@+id/emailCheckMark"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/tick_icon"
                android:visibility="gone" />
        </LinearLayout>

        <!-- Password input -->
        <LinearLayout
            android:id="@+id/layoutPassword"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="32dp"
            android:layout_marginTop="24dp"
            android:background="@drawable/edit_text_background"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingVertical="8dp"
            app:layout_constraintTop_toBottomOf="@id/layoutEmail">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:alpha="0.6"
                android:src="@drawable/key_icon_for_password" />

            <EditText
                android:id="@+id/etPassword"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:background="@null"
                android:hint="**********"
                android:inputType="textPassword"
                android:textColorHint="#7b7b7b"
                android:textColor="#7b7b7b"
                android:textSize="16sp" />

            <ImageView
                android:id="@+id/passwordVisibilityToggle"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:alpha="0.6"
                android:src="@drawable/eye_icon_password_visibility" />
        </LinearLayout>

        <TextView
            android:id="@+id/textViewForgotPassword"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="32dp"
            android:text="Forgot Password?"
            android:textColor="#7b7b7b"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layoutPassword" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnLogin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="32dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/button_dark_bg"
            android:padding="16dp"
            android:text="Log In"
            android:textAllCaps="false"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold"
            android:stateListAnimator="@null"
            app:layout_constraintTop_toBottomOf="@id/textViewForgotPassword" />

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:indeterminateTint="#FFFFFF"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btnBack" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView> 