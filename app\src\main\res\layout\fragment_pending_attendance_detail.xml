<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@android:color/black"
    tools:context=".fragments.PendingAttendanceDetailFragment">

    <ImageView
        android:id="@+id/submittedImageView"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:scaleType="centerCrop"
        tools:src="@tools:sample/avatars" />

    <TextView
        android:id="@+id/rollNumberTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@android:color/white"
        tools:text="Roll Number: 2401MC03" />

    <TextView
        android:id="@+id/timestampTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textColor="@android:color/white"
        android:background="#33D3D3D3"  />

    <TextView
        android:id="@+id/locationTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:textColor="@android:color/white"
        android:background="#33D3D3D3"  />

    <TextView
        android:id="@+id/approvalStatusTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textColor="@android:color/white"
        tools:text="Status: Pending" />

    <Button
        android:id="@+id/approveButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Approve" />

    <Button
        android:id="@+id/rejectButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="Reject" />

</LinearLayout> 