<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="12dp"
    android:background="?attr/selectableItemBackground">

    <ImageView
        android:id="@+id/image_user_avatar"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:background="@drawable/bg_circle_avatar"
        android:padding="2dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_profile"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view_admin_border"
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:background="@drawable/bg_golden_border"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/image_user_avatar"
        app:layout_constraintEnd_toEndOf="@id/image_user_avatar"
        app:layout_constraintStart_toStartOf="@id/image_user_avatar"
        app:layout_constraintTop_toTopOf="@id/image_user_avatar"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/text_user_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/text_user_type"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/image_user_avatar"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="John Doe" />

    <TextView
        android:id="@+id/text_user_type"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#AAAAAA"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/text_user_name"
        app:layout_constraintStart_toStartOf="@+id/text_user_name"
        app:layout_constraintTop_toBottomOf="@+id/text_user_name"
        tools:text="Student" />

</androidx.constraintlayout.widget.ConstraintLayout> 