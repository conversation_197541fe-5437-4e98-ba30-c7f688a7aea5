<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".StudentDataActivity">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="TWApp Database Explorer"
        android:textSize="18sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:tabBackground="@color/white"
        app:tabTextColor="#666666"
        app:tabSelectedTextColor="#3F51B5"
        app:tabIndicatorColor="#3F51B5"
        android:layout_marginBottom="8dp"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Search by:"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="8dp" />

        <Spinner
            android:id="@+id/spinner_criteria"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_gravity="center_vertical" />
    </LinearLayout>

    <EditText
        android:id="@+id/edit_text_query"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Enter search query"
        android:layout_marginBottom="8dp" />

    <Button
        android:id="@+id/button_search"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Search"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/text_view_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Ready to search"
        android:gravity="center"
        android:layout_marginBottom="8dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

</LinearLayout> 