<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    tools:context=".activities.ImageViewActivity">

    <!-- Full-screen image -->
    <com.github.chrisbanes.photoview.PhotoView
        android:id="@+id/full_image_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:contentDescription="Image"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Loading progress -->
    <ProgressBar
        android:id="@+id/loading_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminateTint="@android:color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Back button - moved after other elements to ensure it's on top -->
    <ImageView
        android:id="@+id/back_button"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_margin="16dp"
        android:clickable="true"
        android:contentDescription="Back"
        android:elevation="8dp"
        android:focusable="true"
        android:foreground="?attr/selectableItemBackgroundBorderless"
        android:padding="16dp"
        android:src="@drawable/ic_close"
        app:tint="@android:color/white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
        
    <!-- Download button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/download_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:clickable="true"
        android:contentDescription="Download Image"
        android:focusable="true"
        android:src="@android:drawable/ic_menu_save"
        app:backgroundTint="@android:color/holo_blue_dark"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:tint="@android:color/white" />

    <!-- Download progress -->
    <ProgressBar
        android:id="@+id/download_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:indeterminateTint="@android:color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 