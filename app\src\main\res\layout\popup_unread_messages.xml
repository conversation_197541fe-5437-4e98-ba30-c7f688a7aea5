<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="300dp"
    android:layout_height="wrap_content"
    app:cardCornerRadius="10dp"
    app:cardElevation="8dp"
    app:cardBackgroundColor="#0D0302">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="#0D0302">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Unread Messages"
            android:textStyle="bold"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:gravity="center"
            android:paddingBottom="8dp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#444444" />

        <!-- Direct Messages Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Direct Messages"
            android:textStyle="bold"
            android:textColor="#006BFF"
            android:textSize="16sp"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="4dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_direct_messages"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxHeight="200dp"
            android:nestedScrollingEnabled="true" />

        <TextView
            android:id="@+id/text_no_direct_messages"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="No unread direct messages"
            android:textColor="#AAAAAA"
            android:textSize="14sp"
            android:gravity="center"
            android:visibility="gone"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="12dp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#444444"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp" />

        <!-- Group Messages Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Group Messages"
            android:textStyle="bold"
            android:textColor="#FFCC00"
            android:textSize="16sp"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="4dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_group_messages"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxHeight="200dp"
            android:nestedScrollingEnabled="true" />

        <TextView
            android:id="@+id/text_no_group_messages"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="No unread group messages"
            android:textColor="#AAAAAA"
            android:textSize="14sp"
            android:gravity="center"
            android:visibility="gone"
            android:layout_marginTop="4dp" />

    </LinearLayout>

</androidx.cardview.widget.CardView> 