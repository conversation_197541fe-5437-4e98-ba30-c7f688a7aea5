package com.phad.chatapp.viewmodels

import android.app.Application
import android.graphics.Bitmap
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.phad.chatapp.models.AttendanceEvent
import com.phad.chatapp.models.AttendanceSession
import com.phad.chatapp.models.AttendeeRecord
import com.phad.chatapp.models.QRAttendanceData
import com.phad.chatapp.repositories.AttendanceQRRepository
import com.phad.chatapp.services.QRAttendanceService
import com.phad.chatapp.services.QRValidationResult
import com.phad.chatapp.utils.QRAttendanceDebugUtils
import com.phad.chatapp.utils.SessionManager
import com.phad.chatapp.utils.DeviceIdentificationUtils
import com.phad.chatapp.utils.DuplicateType
import com.phad.chatapp.utils.DeviceDuplicateTestUtils
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.Job

/**
 * ViewModel for managing QR-based attendance system
 * Follows MVVM pattern established in the app
 */
class QRAttendanceViewModel(private val application: Application) : ViewModel() {
    private val TAG = "QRAttendanceViewModel"
    
    // Dependencies
    private val repository = AttendanceQRRepository()
    private val qrService = QRAttendanceService()
    private val sessionManager = SessionManager(application)
    
    // UI State for Admin (Take Attendance)
    private val _adminUiState = MutableStateFlow(AdminQRUiState())
    val adminUiState: StateFlow<AdminQRUiState> = _adminUiState.asStateFlow()
    
    // UI State for Student (Give Attendance)
    private val _studentUiState = MutableStateFlow(StudentQRUiState())
    val studentUiState: StateFlow<StudentQRUiState> = _studentUiState.asStateFlow()
    
    // Current QR generation job
    private var qrGenerationJob: Job? = null
    
    // Current session listener job
    private var sessionListenerJob: Job? = null
    
    init {
        Log.d(TAG, "QRAttendanceViewModel initialized")
        loadUserInfo()
    }
    
    /**
     * Load current user information
     */
    private fun loadUserInfo() {
        val userType = sessionManager.fetchUserType()
        val userId = sessionManager.fetchUserId()
        val userName = sessionManager.fetchUserName()

        Log.d(TAG, "User info - Type: '$userType', ID: '$userId', Name: '$userName'")

        // Check admin status with detailed logging
        val isAdmin = userType == "Admin1" || userType == "Admin2" || userType == "Admin"
        Log.d(TAG, "Admin check: userType='$userType', isAdmin=$isAdmin")
        Log.d(TAG, "Admin1 check: ${userType == "Admin1"}, Admin2 check: ${userType == "Admin2"}, Admin check: ${userType == "Admin"}")

        _adminUiState.value = _adminUiState.value.copy(
            adminId = userId,
            adminName = userName,
            isAdmin = isAdmin
        )

        _studentUiState.value = _studentUiState.value.copy(
            studentId = userId,
            studentName = userName,
            isStudent = userType == "Student"
        )

        // Log final student UI state
        Log.d(TAG, "Final StudentQRUiState: studentId='${_studentUiState.value.studentId}', studentName='${_studentUiState.value.studentName}', isStudent=${_studentUiState.value.isStudent}")

        // Log final UI state
        Log.d(TAG, "Final AdminQRUiState: isAdmin=${_adminUiState.value.isAdmin}, isSessionActive=${_adminUiState.value.isSessionActive}")
    }
    
    /**
     * Load available events for attendance
     */
    fun loadAvailableEvents() {
        viewModelScope.launch {
            try {
                _adminUiState.value = _adminUiState.value.copy(isLoading = true)
                
                val result = repository.getAvailableEvents()
                if (result.isSuccess) {
                    val events = result.getOrNull() ?: emptyList()
                    _adminUiState.value = _adminUiState.value.copy(
                        availableEvents = events,
                        isLoading = false
                    )
                    Log.d(TAG, "Loaded ${events.size} available events")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "Failed to load events"
                    _adminUiState.value = _adminUiState.value.copy(
                        isLoading = false,
                        errorMessage = error
                    )
                    Log.e(TAG, "Error loading events: $error")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Exception loading events", e)
                _adminUiState.value = _adminUiState.value.copy(
                    isLoading = false,
                    errorMessage = e.message ?: "Unknown error"
                )
            }
        }
    }

    /**
     * Create a new attendance event
     */
    fun createAttendanceEvent(name: String, description: String, eventDate: java.util.Date, openingTime: java.util.Date, closingTime: java.util.Date, hours: Int) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "Creating attendance event: $name")
                _adminUiState.value = _adminUiState.value.copy(
                    isCreatingEvent = true,
                    errorMessage = null
                )

                // Generate document ID using the new format
                val documentId = com.phad.chatapp.utils.AttendanceEventUtils.generateDocumentId(eventDate, name)

                // Create new date/time format data
                val (dateString, timeRangeString) = com.phad.chatapp.utils.AttendanceEventUtils.createNewFormatEventTimeData(eventDate, openingTime, closingTime)

                Log.d(TAG, "Creating event with:")
                Log.d(TAG, "  Date string: $dateString")
                Log.d(TAG, "  Time range: $timeRangeString")

                val event = AttendanceEvent(
                    id = documentId,
                    eventDate = dateString,
                    eventTime = timeRangeString,
                    hours = hours,
                    description = description.trim(),
                    createdBy = _adminUiState.value.adminId,
                    creatorName = _adminUiState.value.adminName,
                    createdAt = com.google.firebase.Timestamp.now(),
                    attendees = emptyList(),
                    closedAt = null,
                    _isLive = true // Explicitly set to true for new events
                )

                val result = repository.createAttendanceEvent(event)
                result.fold(
                    onSuccess = { eventId ->
                        Log.d(TAG, "Event created successfully with ID: $eventId")
                        _adminUiState.value = _adminUiState.value.copy(
                            isCreatingEvent = false,
                            showCreateEventDialog = false,
                            createEventSuccess = true,
                            errorMessage = null
                        )
                        // Refresh events list to show the newly created event
                        loadAvailableEvents()
                    },
                    onFailure = { error ->
                        Log.e(TAG, "Error creating event", error)
                        _adminUiState.value = _adminUiState.value.copy(
                            isCreatingEvent = false,
                            errorMessage = "Failed to create event: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "Error creating event", e)
                _adminUiState.value = _adminUiState.value.copy(
                    isCreatingEvent = false,
                    errorMessage = "Error creating event: ${e.message}"
                )
            }
        }
    }

    /**
     * Show create event dialog
     */
    fun showCreateEventDialog() {
        _adminUiState.value = _adminUiState.value.copy(
            showCreateEventDialog = true,
            createEventSuccess = false,
            errorMessage = null
        )
    }

    /**
     * Hide create event dialog
     */
    fun hideCreateEventDialog() {
        _adminUiState.value = _adminUiState.value.copy(
            showCreateEventDialog = false,
            createEventSuccess = false,
            errorMessage = null
        )
    }

    /**
     * Clear create event success state
     */
    fun clearCreateEventSuccess() {
        _adminUiState.value = _adminUiState.value.copy(
            createEventSuccess = false
        )
    }

    /**
     * Force refresh user info and UI state (for debugging)
     */
    fun refreshUserInfo() {
        Log.d(TAG, "Force refreshing user info...")
        loadUserInfo()
    }

    /**
     * Start attendance session for selected event (now simplified for consolidated schema)
     */
    fun startAttendanceSession(event: AttendanceEvent) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "Starting attendance session for event: ${event.getEventName()}")

                _adminUiState.value = _adminUiState.value.copy(isLoading = true)

                // In the consolidated schema, we don't create separate session documents
                // The event itself serves as the "session"
                val sessionId = event.id // Use event ID as session ID

                _adminUiState.value = _adminUiState.value.copy(
                    selectedEvent = event,
                    isSessionActive = true,
                    isLoading = false
                )

                // Register session for security validation
                Log.d(TAG, "=== ADMIN ID FLOW DEBUG ===")
                Log.d(TAG, "Current admin ID from UI state: ${_adminUiState.value.adminId}")
                Log.d(TAG, "Event created by: ${event.createdBy}")
                Log.d(TAG, "Event creator name: ${event.creatorName}")
                Log.d(TAG, "Session manager user ID: ${sessionManager.fetchUserId()}")
                Log.d(TAG, "Session manager user type: ${sessionManager.fetchUserType()}")
                Log.d(TAG, "Session manager user name: ${sessionManager.fetchUserName()}")
                Log.d(TAG, "============================")

                Log.d(TAG, "Registering session - SessionId: $sessionId, AdminId: ${_adminUiState.value.adminId}, EventId: ${event.id}")
                qrService.registerSession(sessionId, _adminUiState.value.adminId, event.id)
                Log.d(TAG, "Session registration completed")

                // Start QR code generation
                Log.d(TAG, "Starting QR generation with SessionId: $sessionId, EventId: ${event.id}")
                startQRGeneration(sessionId, event.id)

                // Start listening to event updates (instead of session updates)
                startEventListener(event.id)

                Log.d(TAG, "Attendance session started successfully for consolidated event")
            } catch (e: Exception) {
                Log.e(TAG, "Exception starting session", e)
                _adminUiState.value = _adminUiState.value.copy(
                    isLoading = false,
                    errorMessage = e.message ?: "Unknown error"
                )
            }
        }
    }
    
    /**
     * Start dynamic QR code generation
     */
    private fun startQRGeneration(sessionId: String, eventId: String) {
        qrGenerationJob?.cancel()
        qrGenerationJob = viewModelScope.launch {
            try {
                qrService.generateDynamicQRCodes(
                    sessionId = sessionId,
                    eventId = eventId,
                    adminId = _adminUiState.value.adminId
                ).collectLatest { (qrData, bitmap) ->
                    _adminUiState.value = _adminUiState.value.copy(
                        currentQRCode = bitmap,
                        currentQRData = qrData,
                        qrRefreshCount = _adminUiState.value.qrRefreshCount + 1
                    )

                    // Update repository with new QR ID
                    repository.updateSessionQRCode(sessionId, qrData.qrId)

                    Log.d(TAG, "QR code updated - ID: ${qrData.qrId}")
                }
            } catch (e: kotlinx.coroutines.CancellationException) {
                // Don't show error for intentional cancellation (e.g., when ending session)
                Log.d(TAG, "QR generation cancelled (intentional)")
                throw e // Re-throw to properly handle coroutine cancellation
            } catch (e: Exception) {
                // Only show error for actual failures, not cancellation
                Log.e(TAG, "Error in QR generation", e)
                _adminUiState.value = _adminUiState.value.copy(
                    errorMessage = "QR generation failed: ${e.message}"
                )
            }
        }
    }
    
    /**
     * Start listening to event updates for real-time attendance count
     */
    private fun startEventListener(eventId: String) {
        sessionListenerJob?.cancel()
        sessionListenerJob = viewModelScope.launch {
            try {
                repository.listenToAttendanceEvent(eventId).collectLatest { event ->
                    event?.let {
                        _adminUiState.value = _adminUiState.value.copy(
                            selectedEvent = it,
                            attendeeCount = it.totalMarked
                        )
                        Log.d(TAG, "Event updated - Attendees: ${it.totalMarked}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error listening to event updates", e)
            }
        }
    }

    /**
     * Legacy method for backward compatibility
     */
    @Deprecated("Use startEventListener instead")
    private fun startSessionListener(sessionId: String) {
        startEventListener(sessionId)
    }
    
    /**
     * End current attendance session
     */
    fun endAttendanceSession() {
        viewModelScope.launch {
            try {
                // Use selectedEvent.id as sessionId since in consolidated schema, event ID = session ID
                val sessionId = _adminUiState.value.selectedEvent?.id
                if (sessionId != null) {
                    Log.d(TAG, "Ending attendance session: $sessionId")

                    // Clear any existing error messages first to prevent stale errors from showing
                    _adminUiState.value = _adminUiState.value.copy(errorMessage = null)

                    // Stop QR generation and session listener FIRST to prevent cancellation errors
                    qrGenerationJob?.cancel()
                    sessionListenerJob?.cancel()

                    // End session in security validator
                    qrService.endSession(sessionId)

                    val result = repository.endAttendanceSession(sessionId)
                    if (result.isSuccess) {
                        _adminUiState.value = _adminUiState.value.copy(
                            isSessionActive = false,
                            selectedEvent = null,
                            currentQRCode = null,
                            currentQRData = null,
                            errorMessage = null // Ensure no error messages remain
                        )

                        Log.d(TAG, "Attendance session ended successfully")
                    } else {
                        val error = result.exceptionOrNull()?.message ?: "Failed to end session"
                        _adminUiState.value = _adminUiState.value.copy(errorMessage = error)
                        Log.e(TAG, "Error ending session: $error")
                    }
                } else {
                    Log.w(TAG, "No active session to end - selectedEvent is null")
                    // Still update UI state to ensure clean state
                    qrGenerationJob?.cancel()
                    sessionListenerJob?.cancel()

                    _adminUiState.value = _adminUiState.value.copy(
                        isSessionActive = false,
                        selectedEvent = null,
                        currentQRCode = null,
                        currentQRData = null,
                        errorMessage = null // Clear any existing errors
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "Exception ending session", e)
                _adminUiState.value = _adminUiState.value.copy(
                    errorMessage = e.message ?: "Unknown error"
                )
            }
        }
    }
    
    /**
     * Process scanned QR code for student attendance
     */
    fun processScannedQR(qrText: String) {
        viewModelScope.launch {
            try {
                val processingStartTime = System.currentTimeMillis()
                Log.d(TAG, "Processing scanned QR code for student: ${_studentUiState.value.studentId}")
                Log.d(TAG, "QR Text length: ${qrText.length}")
                Log.d(TAG, "QR Text (first 200 chars): ${qrText.take(200)}")
                Log.d(TAG, "Processing started at: $processingStartTime")

                // Reset state for new processing
                _studentUiState.value = _studentUiState.value.copy(
                    isProcessing = true,
                    scanResult = null,
                    cameraExited = false,
                    navigatedToResult = false
                )

                val validationStartTime = System.currentTimeMillis()
                Log.d(TAG, "Starting QR validation at: $validationStartTime")
                Log.d(TAG, "Time from processing start to validation: ${validationStartTime - processingStartTime}ms")

                val result = qrService.validateQRCode(qrText, _studentUiState.value.studentId)

                val validationEndTime = System.currentTimeMillis()
                Log.d(TAG, "QR validation completed at: $validationEndTime")
                Log.d(TAG, "Validation took: ${validationEndTime - validationStartTime}ms")
                Log.d(TAG, "Total processing time: ${validationEndTime - processingStartTime}ms")

                if (result.isSuccess) {
                    val validationResult = result.getOrNull()!!

                    Log.d(TAG, "QR validation result - Valid: ${validationResult.isValid}, Reason: ${validationResult.reason}")

                    if (validationResult.qrData != null) {
                        Log.d(TAG, "QR Data - SessionId: ${validationResult.qrData.sessionId}, EventId: ${validationResult.qrData.eventId}, AdminId: ${validationResult.qrData.adminId}")
                        Log.d(TAG, "QR Data - QrId: ${validationResult.qrData.qrId}, Timestamp: ${validationResult.qrData.timestamp}")
                        Log.d(TAG, "QR Data - Age: ${validationResult.qrData.getAgeInSeconds()}s, Remaining: ${validationResult.qrData.getRemainingValiditySeconds()}s")
                    }

                    if (validationResult.isValid && validationResult.qrData != null) {
                        Log.d(TAG, "QR validation successful, marking attendance")
                        // Mark attendance
                        markStudentAttendance(validationResult.qrData)
                    } else {
                        // QR validation failed - reject the scan attempt
                        _studentUiState.value = _studentUiState.value.copy(
                            isProcessing = false,
                            scanResult = ScanResult.Error(validationResult.reason)
                        )
                        Log.w(TAG, "QR validation failed: ${validationResult.reason}")

                        // Log security-related failures for monitoring
                        if (validationResult.reason.contains("expired", ignoreCase = true)) {
                            Log.w(TAG, "SECURITY: Expired QR code rejected - Age exceeded validity window")
                        } else if (validationResult.reason.contains("already used", ignoreCase = true)) {
                            Log.w(TAG, "SECURITY: Replay attack detected - QR code already used")
                        }
                    }
                } else {
                    val error = result.exceptionOrNull()?.message ?: "Validation failed"
                    _studentUiState.value = _studentUiState.value.copy(
                        isProcessing = false,
                        scanResult = ScanResult.Error(error)
                    )
                    Log.e(TAG, "Error validating QR: $error", result.exceptionOrNull())
                }
            } catch (e: Exception) {
                Log.e(TAG, "Exception processing QR", e)
                _studentUiState.value = _studentUiState.value.copy(
                    isProcessing = false,
                    scanResult = ScanResult.Error(e.message ?: "Unknown error")
                )
            }
        }
    }
    
    /**
     * Mark student attendance in the session
     */
    private suspend fun markStudentAttendance(qrData: QRAttendanceData) {
        try {
            Log.d(TAG, "=== STARTING ATTENDANCE MARKING PROCESS ===")
            Log.d(TAG, "Student ID: ${_studentUiState.value.studentId}")
            Log.d(TAG, "Student Name: ${_studentUiState.value.studentName}")
            Log.d(TAG, "Session ID: ${qrData.sessionId}")
            Log.d(TAG, "Event ID: ${qrData.eventId}")
            Log.d(TAG, "QR Code ID: ${qrData.qrId}")

            // Validate student data
            if (_studentUiState.value.studentId.isBlank()) {
                Log.e(TAG, "Student ID is blank!")
                _studentUiState.value = _studentUiState.value.copy(
                    isProcessing = false,
                    scanResult = ScanResult.Error("Student ID not found")
                )
                return
            }

            // Get device ID for duplicate prevention
            val deviceId = DeviceIdentificationUtils.getDeviceId(application)
            Log.d(TAG, "Device ID: ${deviceId.take(16)}...")

            // Get current event to check for duplicates
            val currentEvent = repository.getAttendanceEvent(qrData.eventId).getOrNull()
            if (currentEvent == null) {
                Log.e(TAG, "Event not found: ${qrData.eventId}")
                _studentUiState.value = _studentUiState.value.copy(
                    isProcessing = false,
                    scanResult = ScanResult.Error("Event not found")
                )
                return
            }

            // Check for comprehensive duplicates (user + device)
            val duplicateCheck = qrService.checkComprehensiveDuplicate(
                qrData.sessionId,
                _studentUiState.value.studentId,
                deviceId,
                currentEvent.attendees
            )

            if (duplicateCheck.isDuplicate) {
                Log.w(TAG, "Duplicate attendance detected: ${duplicateCheck.duplicateType}")
                val errorMessage = when (duplicateCheck.duplicateType) {
                    DuplicateType.USER_DUPLICATE -> "You have already marked attendance for this event"
                    DuplicateType.DEVICE_DUPLICATE -> "This device has already been used to mark attendance for this event"
                    else -> duplicateCheck.message
                }

                _studentUiState.value = _studentUiState.value.copy(
                    isProcessing = false,
                    scanResult = ScanResult.Error(errorMessage)
                )
                return
            }

            // Get admin information from database using the admin ID from QR code
            val adminInfo = repository.getUserByRollNumber(qrData.adminId).getOrNull()
            val adminName = adminInfo?.get("name") as? String ?: "Unknown Admin"

            Log.d(TAG, "Admin lookup: adminId='${qrData.adminId}', adminName='$adminName'")

            val attendee = AttendeeRecord(
                rollNumber = _studentUiState.value.studentId,
                name = _studentUiState.value.studentName.ifBlank { "Unknown Student" },
                scanTimestamp = com.google.firebase.Timestamp.now(),
                scannedFrom = com.phad.chatapp.models.ScannedFromAdmin(
                    adminRollNumber = qrData.adminId,
                    adminName = adminName
                ),
                deviceId = deviceId
            )

            Log.d(TAG, "Created AttendeeRecord: rollNumber=${attendee.rollNumber}, studentName=${attendee.name}, deviceId=${attendee.deviceId}")
            Log.d(TAG, "AttendeeRecord validation: isDataValid=${attendee.isDataValid()}")

            Log.d(TAG, "Calling repository.addAttendeeToEvent...")
            val result = repository.addAttendeeToEvent(qrData.eventId, attendee)

            Log.d(TAG, "Repository result: isSuccess=${result.isSuccess}")
            if (result.isFailure) {
                Log.e(TAG, "Repository failure details:", result.exceptionOrNull())
            }

            if (result.isSuccess) {
                // Get event name for success message
                val eventName = try {
                    val event = repository.getAttendanceEvent(qrData.eventId).getOrNull()
                    event?.getEventName() ?: "Unknown Event"
                } catch (e: Exception) {
                    Log.w(TAG, "Could not retrieve event name for success message", e)
                    "Unknown Event"
                }

                _studentUiState.value = _studentUiState.value.copy(
                    isProcessing = false,
                    scanResult = ScanResult.Success("Attendance for $eventName marked successfully!")
                )
                Log.d(TAG, "✅ Attendance marked successfully for ${_studentUiState.value.studentId} - Event: $eventName")
                Log.d(TAG, "=== ATTENDANCE MARKING COMPLETED SUCCESSFULLY ===")
            } else {
                val error = result.exceptionOrNull()?.message ?: "Failed to mark attendance"
                _studentUiState.value = _studentUiState.value.copy(
                    isProcessing = false,
                    scanResult = ScanResult.Error(error)
                )
                Log.e(TAG, "❌ Error marking attendance: $error", result.exceptionOrNull())
                Log.e(TAG, "=== ATTENDANCE MARKING FAILED ===")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception marking attendance", e)
            _studentUiState.value = _studentUiState.value.copy(
                isProcessing = false,
                scanResult = ScanResult.Error(e.message ?: "Unknown error")
            )
        }
    }
    
    /**
     * Clear error messages
     */
    fun clearError() {
        _adminUiState.value = _adminUiState.value.copy(errorMessage = null)
        _studentUiState.value = _studentUiState.value.copy(scanResult = null)
    }

    // dismissSuccessDialog method removed - no longer needed with dedicated result screens

    /**
     * Mark that camera has been exited
     */
    fun markCameraExited() {
        _studentUiState.value = _studentUiState.value.copy(
            cameraExited = true
        )
    }

    /**
     * Mark that navigation to result screen has occurred
     */
    fun markNavigatedToResult() {
        _studentUiState.value = _studentUiState.value.copy(
            navigatedToResult = true
        )
    }

    // clearNavigationFlag method removed - no longer needed with dedicated result screens

    /**
     * Test method to verify attendance marking works
     */
    fun testAttendanceMarking() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "=== TESTING ATTENDANCE MARKING ===")

                // Create a test attendee record
                val testAttendee = AttendeeRecord(
                    rollNumber = "TEST123",
                    name = "Test Student"
                )

                // Get the current session ID from admin UI state (use selectedEvent.id)
                val selectedEvent = _adminUiState.value.selectedEvent
                if (selectedEvent == null) {
                    Log.e(TAG, "No active session for testing")
                    return@launch
                }

                Log.d(TAG, "Testing with session: ${selectedEvent.id}")

                val result = repository.addAttendeeToSession(selectedEvent.id, testAttendee)
                if (result.isSuccess) {
                    Log.d(TAG, "✅ Test attendance marking successful")
                } else {
                    Log.e(TAG, "❌ Test attendance marking failed: ${result.exceptionOrNull()?.message}")
                }

                Log.d(TAG, "=== TEST COMPLETED ===")
            } catch (e: Exception) {
                Log.e(TAG, "Exception during test", e)
            }
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        qrGenerationJob?.cancel()
        sessionListenerJob?.cancel()
        Log.d(TAG, "QRAttendanceViewModel cleared")
    }

    /**
     * Debug method to test QR attendance flow
     */
    fun debugTestQRFlow() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "Starting QR attendance debug test...")

                val testSessionId = "debug_session_${System.currentTimeMillis()}"
                val testEventId = "debug_event_${System.currentTimeMillis()}"
                val adminId = _adminUiState.value.adminId
                val studentId = _studentUiState.value.studentId

                val result = QRAttendanceDebugUtils.testQRFlow(
                    sessionId = testSessionId,
                    eventId = testEventId,
                    adminId = adminId,
                    studentId = studentId,
                    qrService = qrService
                )

                Log.d(TAG, "Debug test completed - Success: ${result.success}")
                if (!result.success) {
                    Log.e(TAG, "Debug test failed:\n${result.report}")
                }

            } catch (e: Exception) {
                Log.e(TAG, "Debug test exception", e)
            }
        }
    }

    /**
     * Close an attendance event manually
     */
    fun closeEvent(event: AttendanceEvent) {
        viewModelScope.launch {
            try {
                val result = repository.closeAttendanceEvent(event.id)
                if (result.isSuccess) {
                    // Reload available events to reflect the change
                    loadAvailableEvents()
                    Log.d(TAG, "Event closed successfully: ${event.getEventName()}")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "Failed to close event"
                    _adminUiState.value = _adminUiState.value.copy(errorMessage = error)
                    Log.e(TAG, "Error closing event: $error")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error closing event", e)
                _adminUiState.value = _adminUiState.value.copy(
                    errorMessage = e.message ?: "Unknown error occurred"
                )
            }
        }
    }

    /**
     * Test device-based duplicate prevention system
     * This method can be called for debugging and validation purposes
     */
    fun testDeviceDuplicatePrevention() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "Starting device duplicate prevention test...")

                val testResult = DeviceDuplicateTestUtils.runAllTests(application)

                if (testResult.success) {
                    Log.d(TAG, "✓ All device duplicate prevention tests PASSED")
                    _adminUiState.value = _adminUiState.value.copy(
                        errorMessage = "Device duplicate prevention tests PASSED"
                    )
                } else {
                    Log.e(TAG, "✗ Some device duplicate prevention tests FAILED")
                    _adminUiState.value = _adminUiState.value.copy(
                        errorMessage = "Device duplicate prevention tests FAILED - Check logs for details"
                    )
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error running device duplicate prevention tests", e)
                _adminUiState.value = _adminUiState.value.copy(
                    errorMessage = "Test execution failed: ${e.message}"
                )
            }
        }
    }

    /**
     * Clean up redundant fields from all events in the database
     * This removes duplicate "totalMarked" and "live" fields that should not exist
     * alongside the proper snake_case fields "total_marked" and "is_live"
     */
    fun cleanupRedundantFields() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "Starting cleanup of redundant fields...")
                _adminUiState.value = _adminUiState.value.copy(isLoading = true)

                val result = repository.cleanupRedundantFields()
                result.fold(
                    onSuccess = { cleanedCount ->
                        Log.d(TAG, "Successfully cleaned $cleanedCount events")
                        _adminUiState.value = _adminUiState.value.copy(
                            isLoading = false,
                            errorMessage = "Cleanup completed. Cleaned $cleanedCount events."
                        )
                    },
                    onFailure = { error ->
                        Log.e(TAG, "Error during cleanup", error)
                        _adminUiState.value = _adminUiState.value.copy(
                            isLoading = false,
                            errorMessage = "Cleanup failed: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "Error in cleanup", e)
                _adminUiState.value = _adminUiState.value.copy(
                    isLoading = false,
                    errorMessage = "Cleanup failed: ${e.message}"
                )
            }
        }
    }

    // ========== Zoom Control Methods ==========

    /**
     * Update zoom level for camera
     */
    fun updateZoomLevel(zoomLevel: Float) {
        val currentState = _studentUiState.value
        val clampedZoom = zoomLevel.coerceIn(currentState.minZoomLevel, currentState.maxZoomLevel)

        Log.d(TAG, "updateZoomLevel called: input=${String.format("%.1f", zoomLevel)}x, " +
                "clamped=${String.format("%.1f", clampedZoom)}x, " +
                "limits=${String.format("%.1f", currentState.minZoomLevel)}x-${String.format("%.1f", currentState.maxZoomLevel)}x")

        _studentUiState.value = currentState.copy(
            currentZoomLevel = clampedZoom
        )

        Log.d(TAG, "Zoom level state updated to: ${String.format("%.1f", clampedZoom)}x")
    }

    /**
     * Set zoom limits based on camera capabilities
     */
    fun setZoomLimits(minZoom: Float, maxZoom: Float) {
        val currentState = _studentUiState.value
        val safeMinZoom = minZoom.coerceAtLeast(1.0f)
        val safeMaxZoom = maxZoom.coerceAtMost(10.0f).coerceAtLeast(safeMinZoom)

        _studentUiState.value = currentState.copy(
            minZoomLevel = safeMinZoom,
            maxZoomLevel = safeMaxZoom,
            currentZoomLevel = currentState.currentZoomLevel.coerceIn(safeMinZoom, safeMaxZoom)
        )

        Log.d(TAG, "Zoom limits set: ${String.format("%.1f", safeMinZoom)}x - ${String.format("%.1f", safeMaxZoom)}x")
    }

    /**
     * Set zooming state for visual feedback
     */
    fun setZoomingState(isZooming: Boolean) {
        Log.d(TAG, "setZoomingState called with isZooming: $isZooming")
        _studentUiState.value = _studentUiState.value.copy(isZooming = isZooming)
    }

    /**
     * Reset zoom to default level
     */
    fun resetZoom() {
        val currentState = _studentUiState.value
        _studentUiState.value = currentState.copy(currentZoomLevel = currentState.minZoomLevel)
        Log.d(TAG, "Zoom reset to ${String.format("%.1f", currentState.minZoomLevel)}x")
    }
}

/**
 * UI State for Admin QR Attendance interface
 */
data class AdminQRUiState(
    val isLoading: Boolean = false,
    val adminId: String = "",
    val adminName: String = "",
    val isAdmin: Boolean = false,
    val availableEvents: List<AttendanceEvent> = emptyList(),
    val selectedEvent: AttendanceEvent? = null,
    val currentSession: AttendanceSession? = null,
    val isSessionActive: Boolean = false,
    val currentQRCode: Bitmap? = null,
    val currentQRData: QRAttendanceData? = null,
    val qrRefreshCount: Int = 0,
    val attendeeCount: Int = 0,
    val errorMessage: String? = null,
    // Event creation states
    val showCreateEventDialog: Boolean = false,
    val isCreatingEvent: Boolean = false,
    val createEventSuccess: Boolean = false
)

/**
 * UI State for Student QR Attendance interface
 */
data class StudentQRUiState(
    val isProcessing: Boolean = false,
    val studentId: String = "",
    val studentName: String = "",
    val isStudent: Boolean = false,
    val scanResult: ScanResult? = null,
    val isCameraPermissionGranted: Boolean = false,

    val cameraExited: Boolean = false,
    val navigatedToResult: Boolean = false,

    // Zoom functionality
    val currentZoomLevel: Float = 1.0f,
    val minZoomLevel: Float = 1.0f,
    val maxZoomLevel: Float = 4.0f,
    val isZooming: Boolean = false
)

/**
 * Sealed class representing scan results
 */
sealed class ScanResult {
    data class Success(val message: String) : ScanResult()
    data class Error(val message: String) : ScanResult()
}
