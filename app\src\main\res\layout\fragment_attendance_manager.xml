<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">

    <!-- Header with title and back button -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@android:color/white"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageButton
                android:id="@+id/backButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_close"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:contentDescription="Back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Attendance Manager"
                android:textColor="@android:color/black"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/backButton"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>

    <!-- Admin View -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/adminView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- Create Event Button -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/createEventFab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:src="@drawable/ic_add"
            android:contentDescription="Create Event"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Live Events List -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/liveEventsRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="16dp"
            android:clipToPadding="false"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/item_live_event" />

        <ProgressBar
            android:id="@+id/adminProgressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/noEventsTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No live events"
            android:textSize="16sp"
            android:textColor="#757575"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Student View -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/studentView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- Event Selection Spinner -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/eventSelectionLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:hint="Select Event"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <AutoCompleteTextView
                android:id="@+id/eventSelectionSpinner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Roll Number Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/rollNumberInputLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:hint="Enter Roll Number"
            app:layout_constraintTop_toBottomOf="@id/eventSelectionLayout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/rollNumberInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Camera Preview -->
        <androidx.camera.view.PreviewView
            android:id="@+id/cameraPreviewView"
            android:layout_width="0dp"
            android:layout_height="300dp"
            android:layout_margin="16dp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/rollNumberInputLayout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Camera Controls -->
        <Button
            android:id="@+id/captureButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Capture"
            android:layout_margin="8dp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/cameraPreviewView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintEnd_toStartOf="@id/switchCameraButton" />

        <Button
            android:id="@+id/switchCameraButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Switch Camera"
            android:layout_margin="8dp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/cameraPreviewView"
            app:layout_constraintStart_toEndOf="@id/captureButton"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Image Preview -->
        <ImageView
            android:id="@+id/imageView"
            android:layout_width="0dp"
            android:layout_height="200dp"
            android:layout_margin="16dp"
            android:scaleType="centerCrop"
            android:background="#EEEEEE"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/rollNumberInputLayout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:contentDescription="Selected Image" />

        <!-- Action Buttons -->
        <Button
            android:id="@+id/cameraButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Take Photo"
            android:layout_margin="16dp"
            app:layout_constraintTop_toBottomOf="@id/imageView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- New Get Location Button -->
        <Button
            android:id="@+id/getLocationButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:text="Get Location"
            app:layout_constraintTop_toBottomOf="@id/cameraButton"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <Button
            android:id="@+id/markAttendanceButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Mark Attendance"
            android:layout_margin="16dp"
            android:enabled="false"
            app:layout_constraintTop_toBottomOf="@id/getLocationButton"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Progress and Status -->
        <ProgressBar
            android:id="@+id/studentProgressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/statusTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:textSize="14sp"
            android:textColor="#757575"
            android:visibility="gone"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@id/markAttendanceButton"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 