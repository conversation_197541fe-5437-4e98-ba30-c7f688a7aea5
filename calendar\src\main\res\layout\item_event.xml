<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/event_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="8dp"
    android:layout_marginTop="4dp"
    android:layout_marginBottom="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardBackgroundColor="@color/teaching_day"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <TextView
            android:id="@+id/event_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.Material3.TitleSmall"
            android:textColor="@android:color/white"
            android:textStyle="bold"
            tools:text="Teaching - Algorithm Design" />

        <TextView
            android:id="@+id/event_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
            android:textColor="@android:color/white"
            android:maxLines="3"
            android:ellipsize="end"
            tools:text="Core teaching session on algorithm design principles" />

    </LinearLayout>

</androidx.cardview.widget.CardView> 