<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="16dp"
    android:paddingTop="12dp"
    android:paddingEnd="16dp"
    android:paddingBottom="12dp"
    android:background="?attr/selectableItemBackground">

    <TextView
        android:id="@+id/user_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="#0D0302"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginEnd="16dp"
        app:layout_constraintEnd_toStartOf="@+id/permission_checkbox"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="John Doe" />

    <TextView
        android:id="@+id/user_roll"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="#78797A"
        android:textSize="14sp"
        android:layout_marginEnd="16dp"
        app:layout_constraintEnd_toStartOf="@+id/permission_checkbox"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/user_name"
        tools:text="AAAAAAAA" />

    <TextView
        android:id="@+id/admin_badge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="#FFCC00"
        android:paddingStart="8dp"
        android:paddingTop="2dp"
        android:paddingEnd="8dp"
        android:paddingBottom="2dp"
        android:text="Admin"
        android:textColor="#0D0302"
        android:textSize="12sp"
        android:textStyle="bold"
        android:visibility="gone"
        android:layout_marginTop="4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/user_roll"
        tools:visibility="visible" />

    <CheckBox
        android:id="@+id/permission_checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:buttonTint="#006BFF"
        android:minWidth="48dp"
        android:minHeight="48dp"
        android:text="Allow"
        android:textColor="#0D0302"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E0E0E0"
        android:layout_marginTop="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/admin_badge" />

</androidx.constraintlayout.widget.ConstraintLayout> 