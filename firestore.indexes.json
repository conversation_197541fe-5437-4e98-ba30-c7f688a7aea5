{"indexes": [{"collectionGroup": "teaching_events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "date", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "general_events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "date", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "leave_applications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "date", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "accepted_leaves", "queryScope": "COLLECTION", "fields": [{"fieldPath": "date", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "NSS_Events_Attendence", "queryScope": "COLLECTION", "fields": [{"fieldPath": "attendees.device_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "NSS_Events_Attendence", "queryScope": "COLLECTION", "fields": [{"fieldPath": "attendees.roll_number", "order": "ASCENDING"}, {"fieldPath": "attendees.device_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "NSS_Events_Attendence", "queryScope": "COLLECTION", "fields": [{"fieldPath": "is_live", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}], "fieldOverrides": []}