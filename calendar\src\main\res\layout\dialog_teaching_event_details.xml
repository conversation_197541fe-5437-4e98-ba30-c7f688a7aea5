<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <TextView
        android:id="@+id/tvEventTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="12dp"
        android:text="Event Title" />

    <TextView
        android:id="@+id/tvEventDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="Event description goes here" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Time: "
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvEventTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="10:00 AM - 12:00 PM" />
            
    </LinearLayout>

    <TextView
        android:id="@+id/tvEventStatus"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="Status: Scheduled" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnApplyForLeave"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Apply for Leave"
        android:layout_marginBottom="8dp"
        app:cornerRadius="8dp"
        style="@style/Widget.MaterialComponents.Button" />
        
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnAcceptClass"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Accept Class"
        app:cornerRadius="8dp"
        app:backgroundTint="@color/slot_booked"
        style="@style/Widget.MaterialComponents.Button" />

</LinearLayout> 