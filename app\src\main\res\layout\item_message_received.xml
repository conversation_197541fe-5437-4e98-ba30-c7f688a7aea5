<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="8dp"
    android:paddingBottom="8dp">

    <!-- Mention badge (small colored indicator) -->
    <View
        android:id="@+id/mention_badge"
        android:layout_width="6dp"
        android:layout_height="0dp"
        android:background="#4CAF50"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/message_card"
        app:layout_constraintStart_toEndOf="@+id/message_card"
        app:layout_constraintTop_toTopOf="@+id/message_card" />

    <!-- Sender name -->
    <TextView
        android:id="@+id/text_message_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="#0D0302"
        android:textSize="12sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Message card -->
    <androidx.cardview.widget.CardView
        android:id="@+id/message_card"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:maxWidth="260dp"
        app:cardBackgroundColor="#EEEEEE"
        app:cardCornerRadius="12dp"
        app:cardElevation="1dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/text_message_name"
        app:layout_constraintWidth_max="wrap">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <!-- Message text -->
            <TextView
                android:id="@+id/text_message_body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#0D0302"
                android:textSize="16sp" />
                
            <!-- Image content for media messages -->
            <ImageView
                android:id="@+id/image_message_content"
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:layout_marginTop="4dp"
                android:adjustViewBounds="true"
                android:scaleType="centerCrop"
                android:visibility="gone"
                android:contentDescription="Image message" />

            <!-- Message time -->
            <TextView
                android:id="@+id/text_message_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginTop="4dp"
                android:textColor="#757575"
                android:textSize="12sp" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout> 