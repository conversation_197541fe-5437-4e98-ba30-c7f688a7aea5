<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <TextView
            android:id="@+id/text_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#000000"
            android:text="Student Name" />

        <TextView
            android:id="@+id/text_roll_number"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="#555555"
            android:text="Roll Number"
            android:layout_marginTop="4dp" />

        <TextView
            android:id="@+id/text_email"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="#555555"
            android:text="Email Address"
            android:layout_marginTop="4dp" />

        <TextView
            android:id="@+id/text_details"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="#777777"
            android:text="Additional details..."
            android:layout_marginTop="8dp" />

    </LinearLayout>

</androidx.cardview.widget.CardView> 